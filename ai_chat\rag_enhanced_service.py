"""
RAG Enhanced AI Service
Tích hợp RAG vào AI service để nâng cao chất lượng trả lời
"""

import logging
from typing import Dict, List, Any, Optional
import json
import time

from .rag_service import rag_knowledge_base, RAGSearchResult
from .smart_ai_service import SmartAIService
from .gemini_service import gemini_service
from .ollama_service import ollama_service

logger = logging.getLogger(__name__)

class RAGEnhancedAIService:
    """
    AI Service được nâng cấp với RAG
    Kết hợp knowledge retrieval với AI generation
    """
    
    def __init__(self):
        self.smart_ai = SmartAIService()
        self.rag_enabled = rag_knowledge_base.available
        
        # RAG configuration
        self.rag_config = {
            'similarity_threshold': 0.7,  # Ngưỡng similarity để sử dụng RAG
            'max_context_length': 2000,   # Max length của context từ RAG
            'top_k_results': 5,           # Số lượng documents retrieve
            'use_rag_for_intents': [      # Intents sử dụng RAG
                'product_search',
                'general_chat', 
                'policy_inquiry',
                'size_inquiry'
            ]
        }
        
        logger.info(f"RAG Enhanced AI Service initialized. RAG enabled: {self.rag_enabled}")
    
    def process_message(self, message: str, user=None, session_id: str = None) -> Dict[str, Any]:
        """
        Process message với RAG enhancement
        """
        try:
            start_time = time.time()
            
            # 1. Detect intent first
            intent_result = self.smart_ai.intent_detector.detect_intent(message)
            intent = intent_result.intent.value if hasattr(intent_result.intent, 'value') else str(intent_result.intent)
            
            # 2. Check if should use RAG
            should_use_rag = (
                self.rag_enabled and 
                intent in self.rag_config['use_rag_for_intents'] and
                len(message.strip()) > 10  # Avoid RAG for very short messages
            )
            
            if should_use_rag:
                # 3. RAG-enhanced processing
                return self._process_with_rag(message, intent, user, session_id)
            else:
                # 4. Fallback to standard processing
                return self.smart_ai.process_message(message, user, session_id)
                
        except Exception as e:
            logger.error(f"Error in RAG enhanced processing: {e}")
            # Fallback to standard AI
            return self.smart_ai.process_message(message, user, session_id)
    
    def _process_with_rag(self, message: str, intent: str, user=None, session_id: str = None) -> Dict[str, Any]:
        """Process message with RAG enhancement"""
        try:
            # 1. Retrieve relevant knowledge
            rag_results = rag_knowledge_base.search(
                query=message,
                n_results=self.rag_config['top_k_results']
            )
            
            # 2. Check if RAG results are relevant
            if not rag_results.documents or max(rag_results.scores) < self.rag_config['similarity_threshold']:
                logger.info("RAG results not relevant enough, using standard processing")
                return self.smart_ai.process_message(message, user, session_id)
            
            # 3. Build enhanced context
            enhanced_context = self._build_rag_context(message, rag_results, intent)
            
            # 4. Generate response with RAG context
            if intent == 'product_search':
                return self._handle_rag_product_search(message, enhanced_context, user, session_id)
            else:
                return self._handle_rag_general_chat(message, enhanced_context, user, session_id)
                
        except Exception as e:
            logger.error(f"Error in RAG processing: {e}")
            return self.smart_ai.process_message(message, user, session_id)
    
    def _build_rag_context(self, message: str, rag_results: RAGSearchResult, intent: str) -> str:
        """Build enhanced context from RAG results"""
        context_parts = []
        
        # Add relevant documents
        for i, (doc, score) in enumerate(zip(rag_results.documents, rag_results.scores)):
            if score >= self.rag_config['similarity_threshold']:
                context_parts.append(f"Thông tin liên quan {i+1}:")
                context_parts.append(doc.content)
                context_parts.append("")  # Empty line for separation
        
        # Limit context length
        full_context = "\n".join(context_parts)
        if len(full_context) > self.rag_config['max_context_length']:
            full_context = full_context[:self.rag_config['max_context_length']] + "..."
        
        return full_context
    
    def _handle_rag_product_search(self, message: str, rag_context: str, user=None, session_id: str = None) -> Dict[str, Any]:
        """Handle product search with RAG context"""
        try:
            # Use Gemini with RAG context for product search
            enhanced_prompt = f"""
Bạn là AI assistant chuyên về thời trang và e-commerce. Dựa vào thông tin sau để trả lời câu hỏi của khách hàng:

THÔNG TIN SẢN PHẨM VÀ CỬA HÀNG:
{rag_context}

CÂUHỎI KHÁCH HÀNG: {message}

Hãy trả lời một cách tự nhiên, hữu ích và chính xác. Nếu có thông tin về sản phẩm cụ thể, hãy đề cập đến tên, giá, thương hiệu. Nếu không tìm thấy sản phẩm phù hợp, hãy gợi ý sản phẩm tương tự hoặc hướng dẫn khách hàng.
"""
            
            # Call Gemini with enhanced prompt
            gemini_response = gemini_service.generate_response(enhanced_prompt, {}, user)
            
            if gemini_response.get('success'):
                # Also get product suggestions from standard search
                standard_result = self.smart_ai.process_message(message, user, session_id)
                
                return {
                    'message': gemini_response['message'],
                    'suggested_products': standard_result.get('suggested_products', []),
                    'actions_taken': ['rag_enhanced_search'],
                    'quick_replies': self._generate_rag_quick_replies(message, rag_context),
                    'metadata': {
                        'intent': 'product_search',
                        'ai_provider': 'gemini_rag',
                        'rag_used': True,
                        'rag_documents_count': len(rag_context.split('\n\n')),
                        'response_time': gemini_response.get('response_time', 0)
                    }
                }
            else:
                # Fallback to standard processing
                return self.smart_ai.process_message(message, user, session_id)
                
        except Exception as e:
            logger.error(f"Error in RAG product search: {e}")
            return self.smart_ai.process_message(message, user, session_id)
    
    def _handle_rag_general_chat(self, message: str, rag_context: str, user=None, session_id: str = None) -> Dict[str, Any]:
        """Handle general chat with RAG context"""
        try:
            # Use Ollama for general chat with RAG context
            enhanced_prompt = f"""
Bạn là AI assistant thân thiện của cửa hàng thời trang online. Dựa vào thông tin sau để trả lời câu hỏi:

THÔNG TIN CỬA HÀNG:
{rag_context}

CÂUHỎI: {message}

Hãy trả lời một cách tự nhiên, thân thiện và hữu ích. Sử dụng thông tin cửa hàng để đưa ra câu trả lời chính xác.
"""
            
            # Try Ollama first for general chat
            if ollama_service.is_available():
                ollama_response = ollama_service.generate_response(enhanced_prompt)
                
                if ollama_response.get('success'):
                    return {
                        'message': ollama_response['message'],
                        'suggested_products': [],
                        'actions_taken': ['rag_enhanced_chat'],
                        'quick_replies': self._generate_rag_quick_replies(message, rag_context),
                        'metadata': {
                            'intent': 'general_chat',
                            'ai_provider': 'ollama_rag',
                            'rag_used': True,
                            'rag_documents_count': len(rag_context.split('\n\n')),
                            'response_time': ollama_response.get('response_time', 0)
                        }
                    }
            
            # Fallback to Gemini
            gemini_response = gemini_service.generate_response(enhanced_prompt, {}, user)
            
            if gemini_response.get('success'):
                return {
                    'message': gemini_response['message'],
                    'suggested_products': [],
                    'actions_taken': ['rag_enhanced_chat'],
                    'quick_replies': self._generate_rag_quick_replies(message, rag_context),
                    'metadata': {
                        'intent': 'general_chat',
                        'ai_provider': 'gemini_rag',
                        'rag_used': True,
                        'rag_documents_count': len(rag_context.split('\n\n')),
                        'response_time': gemini_response.get('response_time', 0)
                    }
                }
            else:
                # Final fallback
                return self.smart_ai.process_message(message, user, session_id)
                
        except Exception as e:
            logger.error(f"Error in RAG general chat: {e}")
            return self.smart_ai.process_message(message, user, session_id)
    
    def _generate_rag_quick_replies(self, message: str, rag_context: str) -> List[str]:
        """Generate smart quick replies based on RAG context"""
        quick_replies = []
        
        # Analyze RAG context to suggest relevant quick replies
        if 'sản phẩm' in rag_context.lower():
            quick_replies.extend(['Xem thêm sản phẩm', 'So sánh giá'])
        
        if 'thương hiệu' in rag_context.lower():
            quick_replies.extend(['Thương hiệu khác', 'Sản phẩm cùng hãng'])
        
        if 'size' in rag_context.lower() or 'kích thước' in rag_context.lower():
            quick_replies.extend(['Hướng dẫn chọn size', 'Bảng size'])
        
        if 'giá' in rag_context.lower():
            quick_replies.extend(['Sản phẩm giá rẻ hơn', 'Khuyến mãi'])
        
        # Default quick replies
        if not quick_replies:
            quick_replies = ['Tìm sản phẩm khác', 'Hỗ trợ thêm', 'Cảm ơn']
        
        return quick_replies[:4]  # Limit to 4 quick replies
    
    def get_rag_status(self) -> Dict[str, Any]:
        """Get RAG system status"""
        return {
            'rag_enabled': self.rag_enabled,
            'knowledge_base_ready': rag_knowledge_base.available,
            'config': self.rag_config
        }

# Global RAG enhanced service instance
rag_enhanced_ai_service = RAGEnhancedAIService()
